<?php
/**
 * 支付宝数据同步系统 Web 管理界面
 *
 * @version 1.0
 */

require_once '../vendor/autoload.php';

use AlipayBillQuery\Services\DataService;
use AlipayBillQuery\Services\Scheduler;
use AlipayBillQuery\Database\Migration;

// 支付宝配置信息
$appId = '2021005119698187';
$privateKey = 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCIAuPG6ks9kh07vX9fHTH/+6jNG1glO5/mqIXQKO/g3Dvu+eYFbket9vZbVAO0TQYd/p2Lzp2WZBmfxocHYDumM/cLvVAEW8sDKkTLOWgV06yvucnbalTltHus43KTcx1KvmybN2wJRxOsaRWAEk+awVkibhpKGliNr4b1ah8XYWCrePGAUUXMRj04vXufhGnSe+E6ryUGwul9ZjdYtLx4mznEHLNvPc7Vojv2R514Jwp1b/q3IqPSIArpaGSeUs79i/R1blC1LrSViniVqnLpp0kRkEuS0/hjDPImEcie0QwjWWfHKQqP30dTTh+gSh7v0Iv+yfOOi/PvcBncKZx1AgMBAAECggEALFAEtgIPkXfRXm1W2j5A1A3B6VFHXpoWdqfhMPilbrVSMYHpl0tevyb/DLJKoquVmqAh5DLk1OK4Fn4v8A9CX9v+WSzMrR7a/aT/1NZXOwVD9dyqD3qNPmmXAbT412Fh4cA40jk0UbF+j2WNQ7SzitADolwM5KfAwii1568zggISOoQdkvR9+ERdW0NbY+6nyQj5fPJeQb+PaSU4XUL+YvH38PBFQ21LSdxC4hpR+btYxu8KJ0a5lhXu8FPVEjshYMhUwwGUUXbt/7mer2Yg6X3zN2q6DUXvQXZAtLSUlzQvxDgIyUJAt61v+T3/xaZpRVLRFaSGsNOXUmRluZAQ4QKBgQDxkfJBYMnzg86WMZqFrToB4PwL8RgKFNUkL9LCHLVxK7kfksuYVJsr8BWvc8rrQuCjhjdBhylddUqeGDJ+8I36ti9UZ4vDgb0S1pMYvQRmUZeC37EKTKUjSsu5hICd8mgY3AuRUNJkFAkuFQMSeHSWe0dz2UyBVoM+oYJ1T8k3UwKBgQCQIsEA/dZYXVlfGGSVXhNmy8uTnCC010pQ2jq3rKiK0kxXvGSAooSIuML46hwn2TpJXW5sYPnvmMl4osEBtaGuP8OSlgJ/7ZP56LHCJeY2nBqOZzngyFdZwQBCkig4Dm1HZF53lfRQCks3YHauWMXpYa/dDn+yOgVZcOSgEJNMFwKBgQCDnr2cGZxvbhWViBllVGkStP8fkpFCjO9E9DmlQfcqXmRTa6w6p36Uhg+KtVCOtrWm424f6gEDxvCNCyoYOAFj5PgMyQ5By+K07Ozgwbwv86zVxgO0VOZ1QD+YKTXa2UUWpm43Ew5PMQt/bDtsSO1dQHZCDNe+cOC5s05dlMdRuQKBgQCIsOzoy9IjKyQ+kxuQrA8qRctiyYYa+rF3y/4zgoK0ZIwSCJAnjfiy0MXW2e6pu9ETEpBOKAnft74Zsf/oZyBV6BLJSYpFWEIllxA9V0PkNlbZBfxVuKlebTKZ75JE1ym7suwD7SotXhXHBqyG25mVoxbtRXrEw1GfaPjo8889MQKBgQDeTTXt6cv4PRB9VN981cqmJJUJVMqpI8CDjpzeZE95G3aljeQcfWBSpD1koudYMaTeWxXWLqt4a+Yz8D4/AAf5tZeAYpm7jksaihR4NVjG9w66JKcCXYa69e5yDIx5+Wlq6QAUWQOyFdttbYYIotGmAmJtHlkkZ+VnXRodRsz/Bg==';
$alipayPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq+s69Posg9bkvSDge58uVtNaSY0msXaAe4LyJTfDp1mvQKlpeRq01ic+yLnGNoEByosOqC4PG+xMzcahU/+1QD4Lnt5Y9p1uzMozGmE2pE2TZcIoaquW75ylxSYURGYHAJ2X5Xk9y1hVKJxLVeDCEY3HfCa+ymlWguSB8DYWE7mJyFtXWrSZOVzfiV6+m1FKVrfWsFBwf+Din8OKBes1AGMZ2xsVoCl+m4Mp9d1j8cbH0OwKTwNUJsOJLEKAxmy6Nkhl+6/fJu7tIAvQn76fi/oTYu71XWAfpoex1TehETBZ/6bNqSr3ztZLZF1fGGpcTr6gWan1Vye6VTzRApnvDwIDAQAB';

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        $response = ['success' => false, 'message' => '', 'data' => null];
        
        switch ($action) {
            case 'get_statistics':
                $dataService = new DataService();
                $stats = $dataService->getDataStatistics(true);
                $response = ['success' => true, 'data' => $stats];
                break;
                
            case 'get_sync_logs':
                $dataService = new DataService();
                $logs = $dataService->getSyncLogs(20, true);
                $response = ['success' => true, 'data' => $logs];
                break;

            case 'manual_sync':
                $dataService = new DataService();
                $success = $dataService->performIncrementalSync($appId, $privateKey, $alipayPublicKey, 60, true);
                $response = ['success' => $success, 'message' => $success ? '智能同步成功' : '同步失败'];
                break;

            case 'check_system':
                $dataService = new DataService();
                $status = $dataService->checkSystemStatus(true);
                $response = ['success' => $status, 'message' => $status ? '系统正常' : '系统异常'];
                break;
                
            case 'create_tables':
                $migration = new Migration();
                $success = $migration->runMigrations();
                $response = ['success' => $success, 'message' => $success ? '表创建成功' : '表创建失败'];
                break;
                
            default:
                $response = ['success' => false, 'message' => '未知操作'];
        }
        
        echo json_encode($response);
        exit;
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝数据同步系统 - Web管理界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stat-label {
            color: #666;
        }
        
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .logs-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-item {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .log-item.error {
            border-left-color: #dc3545;
        }
        
        .log-item.running {
            border-left-color: #ffc107;
        }
        
        .log-time {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .log-content {
            color: #333;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .actions {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 支付宝数据同步系统</h1>
            <p>Web管理界面</p>
        </div>
        
        <div class="main-content">
            <div class="alert alert-success" id="success-alert"></div>
            <div class="alert alert-error" id="error-alert"></div>
            
            <div class="dashboard">
                <div class="card">
                    <h3>📊 数据统计</h3>
                    <div id="statistics-content">
                        <div class="loading">正在加载统计信息...</div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>🔄 系统状态</h3>
                    <div id="system-status">
                        <div class="loading">正在检查系统状态...</div>
                    </div>
                </div>
            </div>
            
            <div class="actions">
                <button class="btn btn-success" onclick="manualSync()">🔄 手动同步</button>
                <button class="btn btn-info" onclick="checkSystem()">🔍 检查系统</button>
                <button class="btn btn-warning" onclick="refreshLogs()">📋 刷新日志</button>
                <button class="btn" onclick="refreshStats()">📊 刷新统计</button>
            </div>
            
            <div class="card">
                <h3>📋 同步日志</h3>
                <div class="logs-container" id="logs-container">
                    <div class="loading">正在加载同步日志...</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>版本：1.0</p>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadSyncLogs();
            checkSystemStatus();
            
            // 每30秒自动刷新一次
            setInterval(function() {
                loadStatistics();
                loadSyncLogs();
            }, 30000);
        });
        
        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertId = type === 'success' ? 'success-alert' : 'error-alert';
            const alertElement = document.getElementById(alertId);
            alertElement.textContent = message;
            alertElement.style.display = 'block';
            
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }
        
        // 发送AJAX请求
        function sendRequest(action, callback) {
            const formData = new FormData();
            formData.append('action', action);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (callback) callback(data);
            })
            .catch(error => {
                console.error('请求失败:', error);
                showAlert('请求失败: ' + error.message, 'error');
            });
        }
        
        // 加载统计信息
        function loadStatistics() {
            sendRequest('get_statistics', function(response) {
                if (response.success) {
                    const stats = response.data;
                    const html = `
                        <div class="stat-item">
                            <span class="stat-label">总记录数</span>
                            <span class="stat-value">${stats.total_records.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">今日新增</span>
                            <span class="stat-value">${stats.today_records.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">成功交易</span>
                            <span class="stat-value">${stats.success_count.toLocaleString()} 笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">成功金额</span>
                            <span class="stat-value">¥${parseFloat(stats.success_amount).toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">最新记录</span>
                            <span class="stat-value">${stats.latest_time || 'N/A'}</span>
                        </div>
                    `;
                    document.getElementById('statistics-content').innerHTML = html;
                } else {
                    document.getElementById('statistics-content').innerHTML = '<div class="loading">加载失败</div>';
                }
            });
        }
        
        // 加载同步日志
        function loadSyncLogs() {
            sendRequest('get_sync_logs', function(response) {
                if (response.success) {
                    const logs = response.data;
                    let html = '';
                    
                    logs.forEach(log => {
                        const statusClass = log.status === 'success' ? '' : (log.status === 'failed' ? 'error' : 'running');
                        const statusIcon = log.status === 'success' ? '✅' : (log.status === 'failed' ? '❌' : '🔄');
                        
                        html += `
                            <div class="log-item ${statusClass}">
                                <div class="log-time">${log.created_at}</div>
                                <div class="log-content">
                                    ${statusIcon} ${log.sync_type} - ${log.status}<br>
                                    时间范围: ${log.start_time} ~ ${log.end_time}<br>
                                    记录数: ${log.total_records} (新增: ${log.new_records}, 更新: ${log.updated_records}, 错误: ${log.error_records})<br>
                                    执行时间: ${log.execution_time} 秒
                                    ${log.error_message ? '<br>错误: ' + log.error_message : ''}
                                </div>
                            </div>
                        `;
                    });
                    
                    document.getElementById('logs-container').innerHTML = html || '<div class="loading">暂无日志</div>';
                } else {
                    document.getElementById('logs-container').innerHTML = '<div class="loading">加载失败</div>';
                }
            });
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            sendRequest('check_system', function(response) {
                const statusHtml = response.success ? 
                    '<div class="stat-item"><span class="stat-label">系统状态</span><span class="stat-value" style="color: #28a745;">✅ 正常</span></div>' :
                    '<div class="stat-item"><span class="stat-label">系统状态</span><span class="stat-value" style="color: #dc3545;">❌ 异常</span></div>';
                
                document.getElementById('system-status').innerHTML = statusHtml;
            });
        }
        
        // 手动同步
        function manualSync() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '🔄 同步中...';
            
            sendRequest('manual_sync', function(response) {
                btn.disabled = false;
                btn.textContent = '🔄 手动同步';
                
                if (response.success) {
                    showAlert('手动同步成功！', 'success');
                    loadStatistics();
                    loadSyncLogs();
                } else {
                    showAlert('手动同步失败: ' + response.message, 'error');
                }
            });
        }
        
        // 检查系统
        function checkSystem() {
            checkSystemStatus();
            showAlert('系统状态已刷新', 'success');
        }
        
        // 刷新日志
        function refreshLogs() {
            loadSyncLogs();
            showAlert('日志已刷新', 'success');
        }
        
        // 刷新统计
        function refreshStats() {
            loadStatistics();
            showAlert('统计信息已刷新', 'success');
        }
    </script>
</body>
</html>
