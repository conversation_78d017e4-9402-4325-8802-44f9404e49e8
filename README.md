# 支付宝数据同步系统

> 一个自动化的支付宝交易数据获取和存储系统
> 作者：程序员（照顾重病父母中）

## 📋 项目简介

这是一个完整的支付宝数据同步解决方案，能够自动从支付宝API获取交易数据并存储到MySQL数据库中。系统支持历史数据导入、实时数据同步、智能更新机制等功能。

## ✨ 功能特性

- ✅ **自动数据同步**: 每分钟自动获取最新交易数据
- ✅ **历史数据导入**: 支持批量导入历史交易记录
- ✅ **智能更新机制**: 超过2个月的数据不再更新，节省资源
- ✅ **数据去重**: 基于支付宝订单号的唯一性约束
- ✅ **完整日志**: 详细的同步日志和错误记录
- ✅ **定时任务**: 自动化的crontab任务管理
- ✅ **数据统计**: 实时的交易统计和分析
- ✅ **错误恢复**: 自动处理API调用失败等异常情况

## 🛠 系统要求

- **PHP**: >= 7.4
- **MySQL**: >= 5.7
- **扩展**: PDO, JSON
- **其他**: Crontab支持（用于定时任务）

## 📦 安装步骤

### 1. 安装依赖

```bash
composer install
```

### 2. 配置数据库

数据库配置文件位于 `config/database.php`：

```php
return [
    'host' => '127.0.0.1',
    'username' => 'root',
    'password' => 'your_password',
    'database' => 'zfb_data',
    'charset' => 'utf8mb4',
    'port' => 3306
];
```

### 3. 配置支付宝应用

在相关文件中配置您的支付宝应用信息：
- 应用ID (AppId)
- 应用私钥 (Private Key)
- 支付宝公钥 (Alipay Public Key)

### 4. 运行安装程序

```bash
php install.php
```

安装程序提供以下功能：
1. 创建数据库表
2. 检查数据库表状态
3. 执行初始数据同步
4. 安装定时任务
5. 系统状态检查

## 🗄 数据库结构

### 主数据表：25_zfb_data

存储支付宝交易数据，包含30个字段：

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| alipay_order_no | varchar(64) | 支付宝订单号（唯一） |
| merchant_order_no | varchar(64) | 商户订单号 |
| gmt_create | datetime | 交易创建时间 |
| total_amount | decimal(10,2) | 交易金额 |
| trade_status | varchar(32) | 交易状态 |
| goods_title | varchar(255) | 商品标题 |
| raw_data | json | 原始数据JSON |

### 同步日志表：zfb_sync_log

记录数据同步过程：

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| sync_type | varchar(32) | 同步类型（initial/incremental） |
| start_time | datetime | 同步开始时间 |
| total_records | int | 总记录数 |
| status | varchar(16) | 同步状态 |
| error_message | text | 错误信息 |

## 🚀 使用方法

### Web管理界面（推荐）

```bash
# 启动Web服务器
php start_web.php

# 浏览器访问
# http://localhost:8080
```

**Web界面功能：**
- 📊 实时数据统计和系统状态
- 🔄 一键手动同步和系统检查
- 📋 同步日志实时查看
- 🔍 数据搜索和分页浏览
- 📱 响应式设计，支持手机访问

### 命令行管理

```bash
# 运行管理界面
php install.php

# 手动执行数据同步
php cron_job.php

# 系统测试
php test_system.php
```

### 查看日志

```bash
# 查看定时任务日志
tail -f logs/scheduler.log

# 查看cron执行日志
tail -f logs/cron.log

# 查看错误日志
tail -f logs/error.log
```

### 定时任务管理

```bash
# 查看当前定时任务
crontab -l

# 编辑定时任务
crontab -e
```

## 📊 数据同步机制

### 初始同步
- 获取指定时间范围的历史数据
- 按月分批处理，避免单次请求过大
- 自动跳过无数据的时间段

### 增量同步
- 每分钟获取最近60分钟的数据
- 智能去重和更新
- 超过2个月的数据不再更新

### 更新策略
1. **新数据**: 直接插入
2. **2个月内数据**: 检查并更新
3. **2个月外数据**: 跳过更新

## 📈 监控和维护

### 系统状态检查

```bash
php install.php  # 选择选项9
```

### 数据统计

```bash
php install.php  # 选择选项7
```

### 同步日志

```bash
php install.php  # 选择选项8
```

## 🧪 测试系统

### 快速测试
```bash
# 快速系统检查
php run_tests.php quick

# 运行所有测试
php run_tests.php all

# 交互式测试菜单
php run_tests.php
```

### 测试模块
- **基础功能测试**: 数据库连接、表结构、数据操作
- **API功能测试**: 支付宝API配置、数据验证、去重机制
- **Web界面测试**: Web文件、页面结构、安全性检查
- **性能测试**: 查询性能、内存使用、并发处理

### 测试演示
```bash
# 查看测试系统演示
php demo_tests.php
```

详细测试文档请查看: [tests/README.md](tests/README.md)

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务状态
   - 验证用户权限

2. **API调用失败**
   - 检查支付宝应用配置
   - 验证网络连接
   - 查看错误日志

3. **定时任务不执行**
   - 检查crontab配置
   - 验证PHP路径
   - 查看cron日志

### 系统诊断
```bash
# 运行系统诊断
php run_tests.php quick

# 查看详细测试
php run_tests.php basic
```

### 日志文件

- `logs/scheduler.log` - 定时任务执行日志
- `logs/cron.log` - Crontab执行日志
- `logs/error.log` - 错误日志
- `logs/test_results.log` - 测试结果日志

## 🔒 安全注意事项

1. **保护配置文件**: 确保数据库和API配置文件安全
2. **定期备份**: 定期备份数据库数据
3. **监控资源**: 关注系统资源使用情况
4. **更新依赖**: 及时更新依赖包

## 📋 项目结构

```
/
├── config/
│   ├── database.php              # 数据库配置
│   └── database.example.php      # 配置模板
├── src/
│   ├── Database/
│   │   ├── Connection.php        # 数据库连接类
│   │   └── Migration.php         # 数据库迁移
│   ├── Models/
│   │   └── ZfbData.php          # 数据模型
│   └── Services/
│       ├── BillFetcher.php      # 数据获取服务
│       ├── DataService.php      # 数据处理服务
│       └── Scheduler.php        # 定时任务调度
├── web/                         # Web管理界面
│   ├── index.php                # 管理首页
│   ├── data.php                 # 数据查看页面
│   └── README.md                # Web界面说明
├── logs/                        # 日志目录
├── cron_job.php                # 定时任务执行脚本
├── install.php                 # 安装和管理脚本
├── test_system.php             # 系统测试脚本
├── start_web.php               # Web服务器启动脚本
└── composer.json               # 依赖配置
```

## 📄 许可证

MIT License

## 👨‍💻 作者

**程序员**

- 版本: 1.0
- 更新时间: 2025-05-31

---

## 💝 致谢

感谢所有理解和支持的朋友们。希望这个项目能够帮助到需要的人。
